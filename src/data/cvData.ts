import type { CVData } from '../types/cv';

export const cvData: CVData = {
  personalInfo: {
    name: "<PERSON><PERSON>",
    title: "UI/UX Designer & FullStack Developer",
    phone: "+34 605 682 559",
    city: "Madrid, España",
    degree: "Grado superior",
    profileImage: "/img/profile-img.jpg",
    logo: "/img/logo.png",
    socialLinks: [
      // {
      //   platform: "website",
      //   url: "https://msarknet.com",
      //   icon: "bx bx-globe"
      // },
      {
        platform: "github",
        url: "https://github.com/MsArk",
        icon: "bx bxl-github"
      },
      {
        platform: "linkedin",
        url: "https://www.linkedin.com/in/ivan-j-206a82110/",
        icon: "bx bxl-linkedin"
      }
    ]
  },
  about: `Soy un trabajador que está acostumbrado al trabajo bajo presión, cuento con amplia
    experiencia en el mundo de la programación, principalmente en el sector sanitario.
    Mi perfil está orientado a front-end aunque también puedo realizar tareas
    tanto back-end y como devops.
    Adicionalmente, cuento con una gran capacidad de análisis de la información y
    experiencia en la gestión de equipos.
    Me considero una persona resolutiva, con buen ánimo y capaz de resolver
    problemas fácilmente.
    Por ahora me encuentro trabajando como autónomo en el sector de los eventos,
    pero sin dejar de lado la programación, ya que sigo realizando páginas web.`,
  skills: [
    // Ofimática
    { name: "Office", percentage: 95, category: "ofimatica" },
    { name: "Office 365", percentage: 95, category: "ofimatica" },
    { name: "XD", percentage: 95, category: "ofimatica" },
    { name: "Figma", percentage: 95, category: "ofimatica" },
    { name: "Mailchimp", percentage: 95, category: "ofimatica" },
    { name: "Acumbamail", percentage: 95, category: "ofimatica" },
    { name: "Zoom", percentage: 95, category: "ofimatica" },
    { name: "Teamviewer", percentage: 95, category: "ofimatica" },
    { name: "Chrome Desktop", percentage: 95, category: "ofimatica" },

    // Sistemas
    { name: "Powershell", percentage: 95, category: "sistem" },
    { name: "Bash", percentage: 95, category: "sistem" },
    { name: "SO Windows", percentage: 95, category: "sistem" },
    { name: "SO Kubuntu / Ubuntu", percentage: 95, category: "sistem" },
    { name: "SO WS 2008 / 2010", percentage: 95, category: "sistem" },
    { name: "SO Ubuntu Server / Debian", percentage: 95, category: "sistem" },

    // APIS y Test
    { name: "Selenium", percentage: 80, category: "apis" },
    { name: "Behat", percentage: 80, category: "apis" },
    { name: "Postman", percentage: 95, category: "apis" },
    { name: "RESTful", percentage: 70, category: "apis" },
    { name: "FastApi", percentage: 70, category: "apis" },

    // Otros
    { name: "Metodología - Scrum", percentage: 80, category: "otro" },
    { name: "Control versiones - Git", percentage: 90, category: "otro" },
    { name: "Control dependencias - Composer", percentage: 90, category: "otro" },
    { name: "Control dependencias - NPM", percentage: 90, category: "otro" },
    { name: "CI / CD - Ansible", percentage: 25, category: "otro" },
    { name: "CI / CD - Jenkins", percentage: 90, category: "otro" },
    { name: "CI / CD - Docker", percentage: 90, category: "otro" },
    { name: "CI / CD - Kubernetes", percentage: 90, category: "otro" },
    { name: "Kanban", percentage: 80, category: "otro" },
    { name: "Asana / Trello", percentage: 90, category: "otro" },
    { name: "OpenAPI / Swagger", percentage: 90, category: "otro" },

    // Programación
    { name: "Java", percentage: 60, category: "prog" },
    { name: "PHP", percentage: 95, category: "prog" },
    { name: "Python", percentage: 40, category: "prog" },
    { name: "Javascript", percentage: 100, category: "prog" },
    { name: "TypeScript", percentage: 90, category: "prog" },

    // Frameworks
    { name: "AngularJS", percentage: 60, category: "frameworks" },
    { name: "Angular", percentage: 70, category: "frameworks" },
    { name: "React.js", percentage: 90, category: "frameworks" },
    { name: "VUE", percentage: 60, category: "frameworks" },
    { name: "Bootstrap", percentage: 100, category: "frameworks" },

    // Webs
    { name: "HTML5, XHTML", percentage: 100, category: "webs" },
    { name: "CSS3", percentage: 100, category: "webs" },
    { name: "XML, XSD, XSL", percentage: 90, category: "webs" },
    { name: "JQuery", percentage: 100, category: "webs" },
    { name: "SCSS / SASS", percentage: 100, category: "webs" },
    { name: "Drupal", percentage: 90, category: "webs" },
    { name: "Django", percentage: 25, category: "webs" },

    // DB
    { name: "MySQL", percentage: 90, category: "db" },
    { name: "MariaDB", percentage: 90, category: "db" },
    { name: "SQLite", percentage: 90, category: "db" },
    { name: "Mongo", percentage: 20, category: "db" },
    { name: "Firebase", percentage: 80, category: "db" }
  ],
  education: [
    {
      title: "Desarrollo de Aplicaciones",
      period: "2013 - 2015",
      institution: "IES Tetuán de las Victorias",
      location: "Madrid, España"
    },
    {
      title: "Sistemas Microinformáticos y Redes",
      period: "2011 - 2013",
      institution: "IES Tetuán de las Victorias",
      location: "Madrid, España"
    }
  ],
  complementaryEducation: [
    {
      title: "Pruebas de accesibilidad web y móvil",
      period: "22 de mayo de 2025",
      institution: "Abstracta Inc.",
      location: "Madrid, España"
    },
    {
      title: "Cerertification Exam - Fireware XTM v11.6 Basics",
      period: "17 de abril de 2013",
      institution: "Watchguard Technologies",
      location: "Madrid, España"
    }
  ],
  experience: [
    {
      title: "Full Stack Developer en Prototypes",
      period: "jul. 2023 - actualidad · 2 año 3 meses",
      company: "Aszendit Tech",
      location: "Madrid, España",
      responsibilities: [
        "Telefónica Aura/Prototypes:",
        "Desarrollo de aplicaciones web con arquitecturas modulares, escalables y mantenibles",
        "Construcción de Agentes de IA y LLM",
        "UX/UI y desarollo de webs en la plataforma AURA",
        "Gestión de despliegues y servicios en Azure (Functions, Storage, App Services)",
        "Integración de la voz cognitiva de Azure en aplicaciones web",
        "Diseño de experiencias conversacionales avanzadas humano-IA en tiempo real",
        "Manejo de firebase",
        "Métricas en GA4 + Looker Studio",
        "Creación de biblioteca de componentes en Storybook"
      ]
    },
    {
      title: "Freelance",
      period: "jul. 2021 - jul. 2023  · 2 años 5 meses",
      company: "",
      location: "",
      responsibilities: [
        "Desarrollo de páginas web",
        "Diseño",
        "Marketing SEO"
      ]
    },
    {
      title: "Headleader, Analista, Full Stack Developer",
      period: "sept. 2022 - jun. 2023  · 10 meses",
      company: "IMAGINADS",
      location: "Madrid, España",
      responsibilities: [
        "Scrum master",
        "Desarrollo FE en angular",
        "Desarollo BE en python",
        "Desarollo de infraestructura de servidores. Montaje de maquinas virtuales y servidores web",
        "Conexión sockets para envio de videos hacia proyectores"
      ]
    },
    {
      title: "Técnico en Audiovisuales - Freelance",
      period: "jul. 2021 - ago. 2022 · 1 año 2 meses",
      company: "Kingston Audiovisuales",
      location: "Madrid, España",
      responsibilities: [
        "Realización de eventos",
        "Montaje de eventos"
      ]
    },
    {
      title: "Developer Full Stack",
      period: "abr. 2015 - jun. 2021 · 6 años 3 meses",
      company: "Navandu Technologies",
      location: "Madrid, España",
      responsibilities: [
        "CMS en drupal"
      ]
    },
    {
      title: "Help Desk",
      period: "abr. 2013 - jun. 2013 · 3 meses",
      company: "Expacom Sistemas SI",
      location: "Madrid, España",
      responsibilities: [
        "Administración de redes locales",
        "Montaje de sistemas operativos",
        "Configuración teléfonos CISCO",
        "Montaje Fireware XTM Watchguard"
      ]
    }
  ],
  portfolio: [
    {
      id: "1",
      title: "Telefónica - Ayuda",
      category: "filter-web",
      coverImage: "/img/portfolio/20_04_23-telefonica_ayuda/ayuda_1.png",
      images: [
        "/img/portfolio/20_04_23-telefonica_ayuda/ayuda_1.png",
        "/img/portfolio/20_04_23-telefonica_ayuda/ayuda_2.png"
      ],
      description: "Sistema de ayuda para Telefónica desarrollado en abril 2023"
    },
    {
      id: "2",
      title: "Telefónica - Storybook",
      category: "filter-web",
      coverImage: "/img/portfolio/23_02_02-telefonica_storybook/storybook_1.png",
      images: [
        "/img/portfolio/23_02_02-telefonica_storybook/storybook_1.png",
        "/img/portfolio/23_02_02-telefonica_storybook/storybook_2.png"
      ],
      description: "Biblioteca de componentes desarrollada con Storybook para Telefónica"
    },
    {
      id: "3",
      title: "Telefónica - Pizarra",
      category: "filter-web",
      coverImage: "/img/portfolio/23_02_15-telefonica_pizarra/pizarra_1.png",
      images: [
        "/img/portfolio/23_02_15-telefonica_pizarra/pizarra_1.png",
        "/img/portfolio/23_02_15-telefonica_pizarra/pizarra_2.png"
      ],
      description: "Aplicación de pizarra digital para Telefónica"
    },
    {
      id: "4",
      title: "Telefónica - LLM Playground",
      category: "filter-web",
      coverImage: "/img/portfolio/23_08_02-telefonica_llm_playground/llm_playground_1.png",
      images: [
        "/img/portfolio/23_08_02-telefonica_llm_playground/llm_playground_1.png",
        "/img/portfolio/23_08_02-telefonica_llm_playground/llm_playground_2.png"
      ],
      description: "Playground para experimentar con modelos de lenguaje LLM"
    },
    {
      id: "5",
      title: "Telefónica - LLM",
      category: "filter-web",
      coverImage: "/img/portfolio/23_10_2-telefonica_llm/llm_1.png",
      images: [
        "/img/portfolio/23_10_2-telefonica_llm/llm_1.png"
      ],
      description: "Implementación de modelos de lenguaje para Telefónica"
    },
    {
      id: "6",
      title: "Telefónica - Notas",
      category: "filter-web",
      coverImage: "/img/portfolio/24_02_21-telefonica_notas/notas_1.png",
      images: [
        "/img/portfolio/24_02_21-telefonica_notas/notas_1.png",
        "/img/portfolio/24_02_21-telefonica_notas/notas_2.png",
        "/img/portfolio/24_02_21-telefonica_notas/notas_3.png"
      ],
      description: "Sistema de gestión de notas para Telefónica"
    },
    {
      id: "7",
      title: "Telefónica - Explora",
      category: "filter-web",
      coverImage: "/img/portfolio/24_11_22-telefonica_explora/explora_1.png",
      images: [
        "/img/portfolio/24_11_22-telefonica_explora/explora_1.png",
        "/img/portfolio/24_11_22-telefonica_explora/explora_2.png"
      ],
      description: "Plataforma de exploración y descubrimiento para Telefónica"
    },
    {
      id: "8",
      title: "Telefónica - Talk Quest",
      category: "filter-web",
      coverImage: "/img/portfolio/25_03_07-telefonica_talk_quest/talk_quest_1.png",
      images: [
        "/img/portfolio/25_03_07-telefonica_talk_quest/talk_quest_1.png",
        "/img/portfolio/25_03_07-telefonica_talk_quest/talk_quest_2.png",
        "/img/portfolio/25_03_07-telefonica_talk_quest/talk_quest_3.png",
        "/img/portfolio/25_03_07-telefonica_talk_quest/talk_quest_4.png"
      ],
      description: "Aplicación conversacional interactiva para Telefónica"
    },
    {
      id: "9",
      title: "Telefónica - Novedades",
      category: "filter-web",
      coverImage: "/img/portfolio/25_03_25-telefonica_novedades/novedades_1.png",
      images: [
        "/img/portfolio/25_03_25-telefonica_novedades/novedades_1.png"
      ],
      description: "Sistema de gestión de novedades y actualizaciones"
    },
    {
      id: "10",
      title: "Telefónica - Enygma",
      category: "filter-web",
      coverImage: "/img/portfolio/25_09_04-telefonica_enygma/enygma_1.png",
      images: [
        "/img/portfolio/25_09_04-telefonica_enygma/enygma_1.png",
        "/img/portfolio/25_09_04-telefonica_enygma/enygma_2.png",
        "/img/portfolio/25_09_04-telefonica_enygma/enygma_3.png",
        "/img/portfolio/25_09_04-telefonica_enygma/enygma_4.png"
      ],
      description: "Proyecto Enygma - Aplicación de inteligencia artificial"
    },
    {
      id: "11",
      title: "Conexión Segura",
      category: "filter-web",
      coverImage: "/img/portfolio/conexion_segura/conexion_segura_1.png",
      images: [
        "/img/portfolio/conexion_segura/conexion_segura_1.png"
      ],
      description: "Sistema de conexión segura y autenticación"
    },
    {
      id: "12",
      title: "Telefónica - Centenario",
      category: "filter-web",
      coverImage: "/img/portfolio/telefonica_centenario/centenario_1.png",
      images: [
        "/img/portfolio/telefonica_centenario/centenario_1.png"
      ],
      description: "Proyecto conmemorativo del centenario de Telefónica"
    },
    {
      id: "13",
      title: "Telefónica - Christmas Tree",
      category: "filter-web",
      coverImage: "/img/portfolio/telefonica_christmas_tree/christmas_tree_1.png",
      images: [
        "/img/portfolio/telefonica_christmas_tree/christmas_tree_1.png",
        "/img/portfolio/telefonica_christmas_tree/christmas_tree_2.png"
      ],
      description: "Aplicación navideña interactiva para Telefónica"
    }
  ],
  services: [
    {
      icon: "FaLaptopCode",
      title: "Desarrollo Full Stack",
      description: "apps web con frontend + backend"
    },
    {
      icon: "FaRobot",
      title: "Agentes de IA y LLM",
      description: "integración de chatbots, asistentes virtuales y flujos conversacionales"
    },
    {
      icon: "FaPalette",
      title: "Diseño UX/UI",
      description: "webs modernas, accesibles y centradas en el usuario"
    },
    {
      icon: "FaCloud",
      title: "Infraestructura y Cloud",
      description: "despliegues en Azure, GCP o servidores propios"
    },
    {
      icon: "FaVideo",
      title: "Audiovisuales y eventos",
      description: "aunque ahora más secundario, puedes incluirlo si sigues ofreciendo"
    },
  ]
};
