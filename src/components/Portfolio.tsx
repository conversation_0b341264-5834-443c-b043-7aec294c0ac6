import React, { useState, useRef } from 'react';
import { Galleria } from 'primereact/galleria';
import type { PortfolioItem } from '../types/cv';

interface PortfolioProps {
  portfolio: PortfolioItem[];
}

const Portfolio: React.FC<PortfolioProps> = ({ portfolio }) => {
  const [activeFilter, setActiveFilter] = useState<string>('filter-web');
  const [activeImages, setActiveImages] = useState<any[]>([]);
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const galleria = useRef<Galleria>(null);

  const portfolioCategories = {
    'filter-games': 'Games',
    'filter-docs': 'Documentation',
    'filter-ia-llm': 'IA / LLM',
    'filter-web': 'Web'
  };

  const filteredPortfolio = activeFilter === '*'
    ? portfolio
    : portfolio.filter(item => item.category === activeFilter);

  // Función para abrir la galería con las imágenes correspondientes
  const openGallery = (clickedItem: PortfolioItem) => {
    // Preparar las imágenes para la galería usando el array images del item clickeado
    const galleryImages = clickedItem.images.map((imageSrc, index) => ({
      itemImageSrc: imageSrc,
      thumbnailImageSrc: imageSrc,
      alt: `${clickedItem.title} - Imagen ${index + 1}`,
      title: clickedItem.title
    }));

    setActiveImages(galleryImages);
    setActiveIndex(0); // Siempre empezar desde la primera imagen del item

    // Mostrar la galería
    setTimeout(() => {
      galleria.current?.show();
    }, 100);
  };

  // Función para manejar el cierre de la galería
  const handleGalleryHide = () => {
    // Limpiar las imágenes activas cuando se cierra la galería
    setActiveImages([]);
    setActiveIndex(0);

    // Forzar la eliminación de cualquier overlay residual
    setTimeout(() => {
      const overlayMasks = document.querySelectorAll('.p-galleria-mask');
      overlayMasks.forEach(mask => {
        if (mask.parentNode) {
          mask.parentNode.removeChild(mask);
        }
      });
    }, 300); // Esperar a que termine la animación de cierre
  };

  // Template para mostrar las imágenes en la galería
  const itemTemplate = (item: any) => {
    return (
      <div className="gallery-item">
        <img
          src={item.itemImageSrc}
          alt={item.alt}
          style={{
            width: '100%',
            height: 'auto',
            maxHeight: '80vh',
            objectFit: 'contain',
            display: 'block'
          }}
        />
        <div className="gallery-caption">
          <h4>{item.title}</h4>
        </div>
      </div>
    );
  };

  const thumbnailTemplate = (item: any) => {
    return (
      <img
        src={item.thumbnailImageSrc}
        alt={item.alt}
        style={{
          width: '60px',
          height: '40px',
          objectFit: 'cover',
          display: 'block',
          borderRadius: '4px'
        }}
      />
    );
  };

  return (
    <section id="portfolio" className="portfolio section-bg">
      <div className="container">
        <div className="section-title">
          <h2 className="text-uppercase">Portfolio</h2>
          {/* <p>
            Magnam dolores commodi suscipit. Necessitatibus eius consequatur ex aliquid fuga eum quidem.
            Sit sint consectetur velit. Quisquam quos quisquam cupiditate. Et nemo qui impedit suscipit
            alias ea. Quia fugiat sit in iste officiis commodi quidem hic quas.
          </p> */}
        </div>

        <div className="row" data-aos="fade-up">
          <div className="col-lg-12 d-flex justify-content-center">
            <ul id="portfolio-flters">
              {Object.entries(portfolioCategories).map(([key, label]) => (
                <li
                  key={key}
                  data-filter={key === '*' ? '*' : `.${key}`}
                  className={activeFilter === key ? 'filter-active' : ''}
                  onClick={() => setActiveFilter(key)}
                >
                  {label}
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="row portfolio-container" data-aos="fade-up" data-aos-delay="100">
          {filteredPortfolio.map((item) => (
            <div
              key={item.id}
              className={`col-lg-4 col-md-6 portfolio-item ${item.category}`}
            >
              <div className="portfolio-wrap">
                <img src={item.coverImage} className="img-fluid" alt={item.title} />
                <div className="portfolio-links">
                  <a
                    onClick={() => openGallery(item)}
                    className="portfolio-lightbox"
                    title={item.title}
                  >
                    <i className="bx bx-plus"></i>
                  </a>

                  {item.link && (
                    <a href={item.link} target="_blank" rel="noopener noreferrer" title="More Details">
                      <i className="bx bx-link"></i>
                    </a>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Galería de PrimeReact */}
        <Galleria
          ref={galleria}
          value={activeImages}
          activeIndex={activeIndex}
          numVisible={7}
          style={{ maxWidth: '850px' }}
          circular
          fullScreen
          showItemNavigators
          showThumbnails
          showItemNavigatorsOnHover
          item={itemTemplate}
          thumbnail={thumbnailTemplate}
          onHide={handleGalleryHide}
          pt={{
            mask: {
              style: {
                backgroundColor: 'rgba(0, 0, 0, 0.9)'
              }
            }
          }}
        />
      </div>
    </section>
  );
};

export default Portfolio;
