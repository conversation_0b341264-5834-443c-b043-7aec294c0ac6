import React from 'react';
import { FaLaptopCode, FaRobot, FaPalette, FaCloud, FaVideo } from 'react-icons/fa';
import type { Service } from '../types/cv';

interface ServicesProps {
  services: Service[];
}

const Services: React.FC<ServicesProps> = ({ services }) => {
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case 'FaLaptopCode':
        return <FaLaptopCode />;
      case 'FaRobot':
        return <FaRobot />;
      case 'FaPalette':
        return <FaPalette />;
      case 'FaCloud':
        return <FaCloud />;
      case 'FaVideo':
        return <FaVideo />;
      default:
        return <FaLaptopCode />; // Fallback
    }
  };

  return (
    <section id="services" className="services">
      <div className="container">
        <div className="section-title">
          <h2 className="text-uppercase">Services</h2>
          {/* <p>
            Magnam dolores commodi suscipit. Necessitatibus eius consequatur ex aliquid fuga eum quidem.
            Sit sint consectetur velit. Quisquam quos quisquam cupiditate. Et nemo qui impedit suscipit
            alias ea. Quia fugiat sit in iste officiis commodi quidem hic quas.
          </p> */}
        </div>

        <div className="row">
          {services.map((service, index) => (
            <div
              key={index}
              className="col-lg-4 col-md-6 icon-box"
              data-aos="fade-up"
              data-aos-delay={index * 100}
            >
              <div className="icon">
                {renderIcon(service.icon)}
              </div>
              <h4 className="title">
                <a href="#">{service.title}</a>
              </h4>
              <p className="description">{service.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
